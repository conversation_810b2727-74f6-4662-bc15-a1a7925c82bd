import { apiClient } from './api';
import { AnalyticsResponse, ActivityItem, AgentPerformance, Conversation, Message } from '../types/api';
import { agentsService } from './agentsService';
import { useAuth } from '../contexts/AuthContext';

export class AnalyticsService {
  // Get dashboard analytics
  async getDashboardAnalytics(businessId?: number): Promise<AnalyticsResponse> {
    const id = businessId || 1;

    try {
      // Use the new real backend analytics endpoint
      const response = await apiClient.get<any>(`/analytics/dashboard/${id}`);
      
      // Transform backend response to frontend format
      return {
        metrics: {
          totalConversations: response.metrics?.totalConversations || 0,
          activeUsers: response.metrics?.activeUsers || 0,
          averageResponseTime: response.metrics?.averageResponseTime || '0s',
          satisfaction: response.metrics?.satisfaction || '0%',
          conversationsToday: response.metrics?.conversationsToday || 0,
          conversationsGrowth: response.metrics?.conversationsGrowth || '+0%'
        },
        recentActivity: (response.recentActivity || []).map((activity: any) => ({
          id: activity.id || '',
          time: activity.time || '',
          action: activity.action || '',
          agent: activity.agent || '',
          details: activity.details || ''
        })),
        agentPerformance: (response.agentPerformance || []).map((agent: any) => ({
          agentId: agent.agentId || '',
          agentName: agent.agentName || '',
          conversations: agent.conversations || 0,
          averageResponseTime: agent.averageResponseTime || '0s',
          satisfaction: agent.satisfaction || '0%',
          status: agent.status || 'inactive'
        }))
      };
    } catch (error) {
      console.error('Failed to get dashboard analytics:', error);
      // Return mock data as fallback
      return this.getMockAnalytics();
    }
  }

  // Get conversation trends for charts
  async getConversationTrends(businessId?: number, days: number = 30): Promise<{
    date: string;
    conversations: number;
  }[]> {
    const id = businessId || 1;
    
    try {
      const response = await apiClient.get<any[]>(`/analytics/trends/${id}?days=${days}`);
      return (response || []).map((trend: any) => ({
        date: trend.date || '',
        conversations: trend.conversations || 0
      }));
    } catch (error) {
      console.error('Failed to get conversation trends:', error);
      // Return mock trend data as fallback
      return this.generateMockTrendData(days);
    }
  }

  // Get recent activity feed
  async getRecentActivity(businessId?: number): Promise<ActivityItem[]> {
    const id = businessId || 1;
    
    try {
      const response = await apiClient.get<any[]>(`/analytics/activity/${id}`);
      return (response || []).map((activity: any) => ({
        id: activity.id || '',
        time: activity.time || '',
        action: activity.action || '',
        agent: activity.agent || '',
        details: activity.details || ''
      }));
    } catch (error) {
      console.error('Failed to get recent activity:', error);
      return this.getMockActivityData();
    }
  }

  // Get agent performance data
  async getAgentPerformance(businessId?: number): Promise<AgentPerformance[]> {
    const id = businessId || 1;
    
    try {
      // Use the existing agent overview endpoint
      const response = await apiClient.get<any>(`/agents/overview?businessId=${id}`);
      
      return (response.agents || []).map((agent: any) => ({
        agentId: agent.id || '',
        agentName: agent.name || '',
        conversations: agent.conversationsToday || 0,
        averageResponseTime: this.formatResponseTime(agent.averageResponseTimeMs || 0),
        satisfaction: `${Math.floor((agent.satisfactionRating || 0) * 20)}%`,
        status: agent.isActive ? 'active' as const : 'inactive' as const
      }));
    } catch (error) {
      console.error('Failed to get agent performance:', error);
      return this.getMockAgentPerformanceData();
    }
  }

  // Test chat endpoint
  async testChat(message: string, agentId?: string, businessId?: number): Promise<{
    response: string;
    success: boolean;
  }> {
    const id = businessId || 1;
    
    try {
      const response = await apiClient.post<any>('/chat/process', {
        businessId: id,
        message,
        agentId,
        conversationId: `test_${Date.now()}`
      });

      return {
        response: response.response || 'Test response received',
        success: true
      };
    } catch (error) {
      console.error('Chat test failed:', error);
      return {
        response: `Mock response to: "${message}". (Backend connection failed)`,
        success: false
      };
    }
  }

  // ==================== PRIVATE HELPER METHODS ====================

  private formatResponseTime(milliseconds: number): string {
    if (milliseconds <= 0) return '0s';
    const seconds = milliseconds / 1000.0;
    return `${seconds.toFixed(1)}s`;
  }

  private generateMockTrendData(days: number): { date: string; conversations: number }[] {
    const trends = [];
    const today = new Date();
    
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      
      trends.push({
        date: date.toISOString().split('T')[0],
        conversations: Math.floor(Math.random() * 50) + 10
      });
    }
    
    return trends;
  }

  private getMockActivityData(): ActivityItem[] {
    return [
      {
        id: '1',
        time: '2 minutes ago',
        action: 'New conversation started',
        agent: 'Customer Support',
        details: 'Web chat conversation initiated'
      },
      {
        id: '2',
        time: '5 minutes ago',
        action: 'Agent response sent',
        agent: 'Sales Assistant',
        details: 'Response time: 1.8s'
      },
      {
        id: '3',
        time: '12 minutes ago',
        action: 'Conversation resolved',
        agent: 'Customer Support',
        details: 'Customer satisfaction: 5/5'
      },
      {
        id: '4',
        time: '18 minutes ago',
        action: 'New user registered',
        agent: '-',
        details: 'Email: <EMAIL>'
      },
      {
        id: '5',
        time: '25 minutes ago',
        action: 'Agent created',
        agent: 'Technical Support',
        details: 'Agent configured and activated'
      }
    ];
  }

  private getMockAgentPerformanceData(): AgentPerformance[] {
    return [
      {
        agentId: 'agent_1',
        agentName: 'Customer Support Agent',
        conversations: 847,
        averageResponseTime: '2.3s',
        satisfaction: '95%',
        status: 'active'
      },
      {
        agentId: 'agent_2',
        agentName: 'Sales Assistant',
        conversations: 234,
        averageResponseTime: '1.8s',
        satisfaction: '92%',
        status: 'inactive'
      }
    ];
  }

  // Mock analytics data (fallback)
  private getMockAnalytics(): AnalyticsResponse {
    return {
      metrics: {
        totalConversations: 1247,
        activeUsers: 89,
        averageResponseTime: '2.1s',
        satisfaction: '94%',
        conversationsToday: 34,
        conversationsGrowth: '+12%'
      },
      recentActivity: this.getMockActivityData(),
      agentPerformance: this.getMockAgentPerformanceData()
    };
  }
}

export const analyticsService = new AnalyticsService(); 