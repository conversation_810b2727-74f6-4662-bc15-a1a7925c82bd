// Backend entity types matching Spring Boot models

export interface Business {
  id: number;
  name: string;
  email: string;
  phone?: string;
  website?: string;
  industry?: string;
  plan: 'STARTER' | 'PROFESSIONAL' | 'ENTERPRISE';
  apiKey: string;
  isActive: boolean;
  settings: BusinessSettings;
  createdAt: string;
  updatedAt: string;
}

export interface BusinessSettings {
  // Business-specific settings (agents are now separate entities)
  theme?: {
    primaryColor?: string;
    secondaryColor?: string;
    fontFamily?: string;
  };
  notifications?: {
    email?: boolean;
    sms?: boolean;
    analytics?: boolean;
    marketing?: boolean;
  };
  preferences?: {
    darkMode?: boolean;
    compactMode?: boolean;
  };
  integrations?: {
    whatsapp?: WhatsAppConfig;
    email?: EmailConfig;
    sms?: SMSConfig;
  };
}

// Updated AgentConfig to match consolidated JSON-embedded structure
export interface AgentConfig {
  id: string;
  businessId: number;
  name: string;
  description?: string;
  agentType: 'SALES' | 'SUPPORT' | 'BOOKING' | 'GENERAL' | 'TECHNICAL' | 'BILLING' | 'MANAGER';
  personality?: string;
  isActive: boolean;
  welcomeMessage?: string;
  fallbackMessage?: string;
  systemPrompt?: string;
  
  // JSON-embedded data structures
  capabilities: Record<string, AgentCapabilityInfo>;
  routingRules: AgentRoutingRuleInfo[];
  schedules: AgentScheduleInfo[];
  
  // Performance metrics (updated by backend)
  conversationsToday: number;
  averageResponseTimeMs: number;
  satisfactionRating: number;
  resolutionRate: number;
  
  createdAt: string;
  updatedAt: string;
}

// JSON-embedded data types (matching backend BusinessAgent inner classes)
export interface AgentCapabilityInfo {
  proficiencyLevel: number; // 1-5 scale
  isPrimary: boolean;
  description?: string;
}

export interface AgentRoutingRuleInfo {
  ruleType: string; // 'channel', 'time', 'intent', 'customer_type', 'keyword'
  condition: string; // 'whatsapp', '09:00-17:00', 'booking_intent', 'vip'
  priority: number; // Higher number = higher priority
  isActive: boolean;
}

export interface AgentScheduleInfo {
  dayOfWeek: number; // 1-7 (Monday-Sunday)
  startTime: string; // "09:00"
  endTime: string; // "17:00"
  timezone: string; // "America/New_York"
  isActive: boolean;
}

// Agent Performance DTO (from backend)
export interface AgentPerformanceDto {
  agentId: string;
  agentName: string;
  conversationsToday: number;
  averageResponseTime: number;
  satisfactionRating: number;
  resolutionRate: number;
  isActive: boolean;
  lastUpdated: string;
}

// Business Agent Overview (from backend)
export interface BusinessAgentOverviewDto {
  businessId: number;
  totalAgents: number;
  activeAgents: number;
  totalConversationsToday: number;
  averageSatisfactionRating: number;
  agents: AgentConfig[];
}

export interface WhatsAppConfig {
  enabled: boolean;
  phoneNumber?: string;
  accessToken?: string;
}

export interface EmailConfig {
  enabled: boolean;
  smtpHost?: string;
  smtpPort?: number;
  username?: string;
}

export interface SMSConfig {
  enabled: boolean;
  provider: 'twilio' | 'other';
  accountSid?: string;
  authToken?: string;
}

export interface Conversation {
  id: number;
  businessId: number;
  customerId: number;
  channel: 'WEB_CHAT' | 'WHATSAPP' | 'EMAIL' | 'SMS';
  status: 'ACTIVE' | 'RESOLVED' | 'PENDING' | 'ESCALATED';
  title?: string;
  summary?: string;
  tags?: string[];
  priorityScore: number;
  satisfactionRating?: number;
  agentId?: number;
  // Multi-agent support
  currentAgentId?: string;
  previousAgentId?: string;
  agentChangesCount: number;
  lastAgentChange?: string;
  startedAt: string;
  endedAt?: string;
  lastActivityAt: string;
  metadata: Record<string, any>;
}

export interface Message {
  id: number;
  conversationId: number;
  sender: 'CUSTOMER' | 'AI' | 'AGENT';
  messageType: 'TEXT' | 'IMAGE' | 'FILE' | 'AUDIO' | 'VIDEO' | 'SYSTEM';
  content: string;
  contentHtml?: string;
  attachments?: any[];
  aiModel?: string;
  tokensUsed?: number;
  processingTimeMs?: number;
  isEdited: boolean;
  editedAt?: string;
  metadata: Record<string, any>;
  createdAt: string;
}

// API Request/Response types (updated for JSON-embedded agents)

export interface CreateAgentRequest {
  businessId: number;
  name: string;
  description?: string;
  agentType: 'SALES' | 'SUPPORT' | 'BOOKING' | 'GENERAL' | 'TECHNICAL' | 'BILLING' | 'MANAGER';
  personality?: string;
  welcomeMessage?: string;
  systemPrompt?: string;
  capabilities?: Record<string, AgentCapabilityInfo>;
  routingRules?: AgentRoutingRuleInfo[];
  schedules?: AgentScheduleInfo[];
}

export interface UpdateAgentRequest {
  name?: string;
  description?: string;
  personality?: string;
  welcomeMessage?: string;
  systemPrompt?: string;
  isActive?: boolean;
  capabilities?: Record<string, AgentCapabilityInfo>;
  routingRules?: AgentRoutingRuleInfo[];
  schedules?: AgentScheduleInfo[];
}

export interface UpdateBusinessRequest {
  name?: string;
  email?: string;
  phone?: string;
  website?: string;
  industry?: string;
  settings?: Partial<BusinessSettings>;
}

export interface AnalyticsResponse {
  metrics: {
    totalConversations: number;
    activeUsers: number;
    averageResponseTime: string;
    satisfaction: string;
    conversationsToday: number;
    conversationsGrowth: string;
  };
  recentActivity: ActivityItem[];
  agentPerformance: AgentPerformance[];
}

export interface ActivityItem {
  id: string;
  time: string;
  action: string;
  agent?: string;
  details?: string;
}

export interface AgentPerformance {
  agentId: string;
  agentName: string;
  conversations: number;
  averageResponseTime: string;
  satisfaction: string;
  status: 'active' | 'inactive';
}

export interface ChatRequest {
  businessId: number;
  conversationId?: number | string;
  agentId?: string; // Add agent ID field
  customerId?: number;
  channel?: string;
  message: string;
  detectedIntent?: string;
  context?: Record<string, any>;
}

export interface ChatResponse {
  response: string;
  conversationId: string; // Change to string to match backend
  agentId?: string;
  agentName?: string;
  agentType?: string;
  handoffOccurred?: boolean;
  responseTime?: number;
  timestamp?: string;
}

// API Error types

export interface ApiError {
  message: string;
  status: number;
  timestamp: string;
  path: string;
} 