import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Tabs, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Settings, ArrowLeft, Save, Key, Bell, Shield, User, Loader2, CheckCircle, AlertCircle, Eye, EyeOff, Copy, Download, Upload, Trash2, Camera, Globe, Mail, Phone, Building2, Sparkles, Activity, Lock, Unlock } from "lucide-react";
import { Link } from "react-router-dom";
import { businessService, Business, BusinessSettings } from "@/services";
import { useAuth } from "@/contexts/AuthContext";

const SettingsPage = () => {
  const { user } = useAuth();
  const [business, setBusiness] = useState<Business | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [showApiKey, setShowApiKey] = useState(false);
  const [copied, setCopied] = useState(false);
  
  const [businessSettings, setBusinessSettings] = useState({
    businessName: "",
    email: "",
    phone: "",
    website: "",
    industry: "",
  });

  const [userProfile, setUserProfile] = useState({
    firstName: user?.firstName || "",
    lastName: user?.lastName || "",
    email: user?.email || "",
    avatar: "",
  });

  const [preferences, setPreferences] = useState({
    emailNotifications: true,
    smsNotifications: false,
    analyticsReports: true,
    marketingEmails: false,
    darkMode: false,
    compactMode: false,
  });

  const [apiSettings, setApiSettings] = useState({
    apiKey: "",
    rateLimitEnabled: true,
    corsEnabled: true,
    webhookUrl: "",
  });

  const [securitySettings, setSecuritySettings] = useState({
    twoFactorEnabled: false,
    sessionTimeout: 30,
    passwordLastChanged: "2024-01-15",
    lastLogin: "2024-01-20 14:30",
    loginHistory: [
      { date: "2024-01-20 14:30", location: "New York, NY", device: "Chrome on MacBook" },
      { date: "2024-01-19 09:15", location: "New York, NY", device: "Chrome on MacBook" },
      { date: "2024-01-18 16:45", location: "New York, NY", device: "Mobile Safari" },
    ]
  });

  useEffect(() => {
    loadBusinessData();
  }, []);

  const loadBusinessData = async () => {
    try {
      setLoading(true);
      setError(null);
      const businessData = await businessService.getBusiness();
      setBusiness(businessData);
      
      // Populate form fields
      setBusinessSettings({
        businessName: businessData.name || "",
        email: businessData.email || "",
        phone: businessData.phone || "",
        website: businessData.website || "",
        industry: businessData.industry || "",
      });

      setApiSettings({
        apiKey: businessData.apiKey || "",
        rateLimitEnabled: true,
        corsEnabled: true,
        webhookUrl: "",
      });

      // Load preferences from settings
      const settings = businessData.settings || {};
      if (settings.notifications) {
        setPreferences({
          emailNotifications: settings.notifications.email ?? true,
          smsNotifications: settings.notifications.sms ?? false,
          analyticsReports: settings.notifications.analytics ?? true,
          marketingEmails: settings.notifications.marketing ?? false,
          darkMode: settings.preferences?.darkMode ?? false,
          compactMode: settings.preferences?.compactMode ?? false,
        });
      }
    } catch (err) {
      console.error('Failed to load business data:', err);
      setError('Failed to load business settings. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleBusinessChange = (field: string, value: string) => {
    setBusinessSettings(prev => ({ ...prev, [field]: value }));
  };

  const handleUserProfileChange = (field: string, value: string) => {
    setUserProfile(prev => ({ ...prev, [field]: value }));
  };

  const handlePreferenceChange = (field: string, value: boolean) => {
    setPreferences(prev => ({ ...prev, [field]: value }));
  };

  const handleApiSettingChange = (field: string, value: any) => {
    setApiSettings(prev => ({ ...prev, [field]: value }));
  };

  const copyApiKey = async () => {
    if (apiSettings.apiKey) {
      await navigator.clipboard.writeText(apiSettings.apiKey);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }
  };

  const regenerateApiKey = async () => {
    // In real app, this would call the backend to regenerate the API key
    const newApiKey = "palai_" + Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
    setApiSettings(prev => ({ ...prev, apiKey: newApiKey }));
  };

  const handleSave = async () => {
    if (!business) return;

    try {
      setSaving(true);
      setError(null);
      setSuccess(null);

      // Update business info
      await businessService.updateBusiness(business.id, {
        name: businessSettings.businessName.trim(),
        email: businessSettings.email.trim(),
        phone: businessSettings.phone.trim() || undefined,
        website: businessSettings.website.trim() || undefined,
        industry: businessSettings.industry.trim() || undefined,
      });

      // Update preferences in settings
      const updatedSettings: BusinessSettings = {
        ...(business.settings || {}),
        notifications: {
          email: preferences.emailNotifications,
          sms: preferences.smsNotifications,
          analytics: preferences.analyticsReports,
          marketing: preferences.marketingEmails,
        },
        preferences: {
          darkMode: preferences.darkMode,
          compactMode: preferences.compactMode,
        }
      };

      await businessService.updateSettings(business.id, updatedSettings);

      // Reload data to reflect changes
      await loadBusinessData();
      
      setSuccess("Settings saved successfully!");
      setTimeout(() => setSuccess(null), 3000);
    } catch (err) {
      console.error('Failed to save settings:', err);
      setError('Failed to save settings. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-subtle flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="relative">
            <div className="w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center mx-auto">
              <Settings className="w-8 h-8 text-white" />
            </div>
            <div className="absolute -top-1 -right-1 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
              <Loader2 className="w-4 h-4 text-white animate-spin" />
            </div>
          </div>
          <div className="space-y-2">
            <h2 className="text-xl font-semibold text-foreground">Loading settings...</h2>
            <p className="text-muted-foreground">Please wait while we fetch your configuration</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-subtle">
      {/* Header */}
      <header className="border-b border-border bg-background/80 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Link to="/dashboard">
                <Button variant="ghost" size="sm">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Dashboard
                </Button>
              </Link>
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center">
                  <Settings className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h1 className="text-xl font-bold text-foreground">Settings</h1>
                  <p className="text-sm text-muted-foreground">Configure your account and preferences</p>
                </div>
              </div>
            </div>
            <Button 
              onClick={handleSave} 
              disabled={saving}
              className="bg-gradient-primary hover:opacity-90"
            >
              {saving ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="w-4 h-4 mr-2" />
                  Save Changes
                </>
              )}
            </Button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto space-y-8">
          {/* Success/Error Messages */}
          {success && (
            <Card className="border-green-200 bg-green-50">
              <CardContent className="pt-6">
                <div className="flex items-center space-x-2 text-green-600">
                  <CheckCircle className="w-5 h-5" />
                  <span className="font-medium">{success}</span>
                </div>
              </CardContent>
            </Card>
          )}

          {error && (
            <Card className="border-red-200 bg-red-50">
              <CardContent className="pt-6">
                <div className="flex items-center space-x-2 text-red-600">
                  <AlertCircle className="w-5 h-5" />
                  <span className="font-medium">{error}</span>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Settings Tabs */}
          <Tabs defaultValue="profile" className="space-y-6">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="profile" className="flex items-center gap-2">
                <User className="w-4 h-4" />
                Profile
              </TabsTrigger>
              <TabsTrigger value="business" className="flex items-center gap-2">
                <Building2 className="w-4 h-4" />
                Business
              </TabsTrigger>
              <TabsTrigger value="notifications" className="flex items-center gap-2">
                <Bell className="w-4 h-4" />
                Notifications
              </TabsTrigger>
              <TabsTrigger value="api" className="flex items-center gap-2">
                <Key className="w-4 h-4" />
                API
              </TabsTrigger>
              <TabsTrigger value="security" className="flex items-center gap-2">
                <Shield className="w-4 h-4" />
                Security
              </TabsTrigger>
            </TabsList>

            {/* Profile Settings */}
            <TabsContent value="profile" className="space-y-6">
              <Card className="border-0 shadow-soft bg-card">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User className="w-5 w-5" />
                    Personal Information
                  </CardTitle>
                  <CardDescription>Update your personal profile and account details</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Avatar Section */}
                  <div className="flex items-center gap-6">
                    <div className="relative">
                      <div className="w-20 h-20 bg-gradient-primary rounded-full flex items-center justify-center text-white text-xl font-semibold">
                        {userProfile.firstName.charAt(0)}{userProfile.lastName.charAt(0)}
                      </div>
                      <Button size="sm" variant="outline" className="absolute -bottom-1 -right-1 w-8 h-8 p-0">
                        <Camera className="w-4 h-4" />
                      </Button>
                    </div>
                    <div className="flex-1">
                      <h3 className="font-medium mb-2">Profile Picture</h3>
                      <p className="text-sm text-muted-foreground mb-3">
                        Upload a profile picture to personalize your account
                      </p>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">
                          <Upload className="w-4 h-4 mr-2" />
                          Upload Photo
                        </Button>
                        <Button variant="outline" size="sm">
                          <Trash2 className="w-4 h-4 mr-2" />
                          Remove
                        </Button>
                      </div>
                    </div>
                  </div>

                  {/* Profile Form */}
                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="firstName">First Name</Label>
                      <Input
                        id="firstName"
                        value={userProfile.firstName}
                        onChange={(e) => handleUserProfileChange("firstName", e.target.value)}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="lastName">Last Name</Label>
                      <Input
                        id="lastName"
                        value={userProfile.lastName}
                        onChange={(e) => handleUserProfileChange("lastName", e.target.value)}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="email">Email Address</Label>
                      <Input
                        id="email"
                        type="email"
                        value={userProfile.email}
                        onChange={(e) => handleUserProfileChange("email", e.target.value)}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="timezone">Timezone</Label>
                      <Input
                        id="timezone"
                        value="UTC-5 (Eastern Time)"
                        disabled
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Business Settings */}
            <TabsContent value="business" className="space-y-6">
              <Card className="border-0 shadow-soft bg-card">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Building2 className="w-5 h-5" />
                    Business Information
                  </CardTitle>
                  <CardDescription>Update your business details and contact information</CardDescription>
                </CardHeader>
                <CardContent className="grid md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="business-name">Business Name</Label>
                    <Input
                      id="business-name"
                      value={businessSettings.businessName}
                      onChange={(e) => handleBusinessChange("businessName", e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email">Email Address</Label>
                    <Input
                      id="email"
                      type="email"
                      value={businessSettings.email}
                      onChange={(e) => handleBusinessChange("email", e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone Number</Label>
                    <Input
                      id="phone"
                      value={businessSettings.phone}
                      onChange={(e) => handleBusinessChange("phone", e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="website">Website</Label>
                    <Input
                      id="website"
                      value={businessSettings.website}
                      onChange={(e) => handleBusinessChange("website", e.target.value)}
                    />
                  </div>
                  <div className="space-y-2 md:col-span-2">
                    <Label htmlFor="industry">Industry</Label>
                    <Input
                      id="industry"
                      value={businessSettings.industry}
                      onChange={(e) => handleBusinessChange("industry", e.target.value)}
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Notification Settings */}
            <TabsContent value="notifications" className="space-y-6">
              <Card className="border-0 shadow-soft bg-card">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Bell className="w-5 h-5" />
                    Notification Preferences
                  </CardTitle>
                  <CardDescription>Choose how and when you want to be notified</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="space-y-1">
                        <Label className="text-base">Email Notifications</Label>
                        <p className="text-sm text-muted-foreground">Receive important updates via email</p>
                      </div>
                      <Switch
                        checked={preferences.emailNotifications}
                        onCheckedChange={(checked) => handlePreferenceChange("emailNotifications", checked)}
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="space-y-1">
                        <Label className="text-base">SMS Notifications</Label>
                        <p className="text-sm text-muted-foreground">Get urgent alerts via text message</p>
                      </div>
                      <Switch
                        checked={preferences.smsNotifications}
                        onCheckedChange={(checked) => handlePreferenceChange("smsNotifications", checked)}
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="space-y-1">
                        <Label className="text-base">Analytics Reports</Label>
                        <p className="text-sm text-muted-foreground">Weekly performance summaries</p>
                      </div>
                      <Switch
                        checked={preferences.analyticsReports}
                        onCheckedChange={(checked) => handlePreferenceChange("analyticsReports", checked)}
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="space-y-1">
                        <Label className="text-base">Marketing Emails</Label>
                        <p className="text-sm text-muted-foreground">Product updates and tips</p>
                      </div>
                      <Switch
                        checked={preferences.marketingEmails}
                        onCheckedChange={(checked) => handlePreferenceChange("marketingEmails", checked)}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Display Preferences */}
              <Card className="border-0 shadow-soft bg-card">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Activity className="w-5 h-5" />
                    Display Preferences
                  </CardTitle>
                  <CardDescription>Customize your interface experience</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="space-y-1">
                        <Label className="text-base">Dark Mode</Label>
                        <p className="text-sm text-muted-foreground">Use dark theme for better eye comfort</p>
                      </div>
                      <Switch
                        checked={preferences.darkMode}
                        onCheckedChange={(checked) => handlePreferenceChange("darkMode", checked)}
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="space-y-1">
                        <Label className="text-base">Compact Mode</Label>
                        <p className="text-sm text-muted-foreground">Reduce spacing for more content</p>
                      </div>
                      <Switch
                        checked={preferences.compactMode}
                        onCheckedChange={(checked) => handlePreferenceChange("compactMode", checked)}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* API Settings */}
            <TabsContent value="api" className="space-y-6">
              <Card className="border-0 shadow-soft bg-card">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Key className="w-5 h-5" />
                    API Configuration
                  </CardTitle>
                  <CardDescription>Manage your API keys and integration settings</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* API Key Section */}
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label>API Key</Label>
                      <div className="flex gap-2">
                        <div className="flex-1 relative">
                          <Input
                            type={showApiKey ? "text" : "password"}
                            value={apiSettings.apiKey}
                            readOnly
                            className="pr-20"
                          />
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="absolute right-0 top-0 h-full px-3"
                            onClick={() => setShowApiKey(!showApiKey)}
                          >
                            {showApiKey ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                          </Button>
                        </div>
                        <Button
                          variant="outline"
                          onClick={copyApiKey}
                          className={copied ? "bg-green-50 border-green-200" : ""}
                        >
                          {copied ? <CheckCircle className="w-4 h-4 text-green-600" /> : <Copy className="w-4 h-4" />}
                        </Button>
                        <Button variant="outline" onClick={regenerateApiKey}>
                          Regenerate
                        </Button>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        Keep your API key secure. Never share it publicly.
                      </p>
                    </div>

                    {/* API Settings */}
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div className="space-y-1">
                          <Label className="text-base">Rate Limiting</Label>
                          <p className="text-sm text-muted-foreground">Prevent API abuse with rate limits</p>
                        </div>
                        <Switch
                          checked={apiSettings.rateLimitEnabled}
                          onCheckedChange={(checked) => handleApiSettingChange("rateLimitEnabled", checked)}
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="space-y-1">
                          <Label className="text-base">CORS Enabled</Label>
                          <p className="text-sm text-muted-foreground">Allow cross-origin requests</p>
                        </div>
                        <Switch
                          checked={apiSettings.corsEnabled}
                          onCheckedChange={(checked) => handleApiSettingChange("corsEnabled", checked)}
                        />
                      </div>
                    </div>

                    {/* Webhook URL */}
                    <div className="space-y-2">
                      <Label htmlFor="webhook">Webhook URL</Label>
                      <Input
                        id="webhook"
                        value={apiSettings.webhookUrl}
                        onChange={(e) => handleApiSettingChange("webhookUrl", e.target.value)}
                        placeholder="https://your-domain.com/webhook"
                      />
                      <p className="text-sm text-muted-foreground">
                        Receive real-time updates about your AI agents
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Security Settings */}
            <TabsContent value="security" className="space-y-6">
              <Card className="border-0 shadow-soft bg-card">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Shield className="w-5 h-5" />
                    Security Settings
                  </CardTitle>
                  <CardDescription>Manage your account security and authentication</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Two-Factor Authentication */}
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="space-y-1">
                        <Label className="text-base">Two-Factor Authentication</Label>
                        <p className="text-sm text-muted-foreground">Add an extra layer of security to your account</p>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className={`text-sm ${securitySettings.twoFactorEnabled ? 'text-green-600' : 'text-muted-foreground'}`}>
                          {securitySettings.twoFactorEnabled ? 'Enabled' : 'Disabled'}
                        </span>
                        <Switch
                          checked={securitySettings.twoFactorEnabled}
                          onCheckedChange={(checked) => setSecuritySettings(prev => ({ ...prev, twoFactorEnabled: checked }))}
                        />
                      </div>
                    </div>
                  </div>

                  {/* Password Management */}
                  <div className="space-y-4">
                    <h3 className="font-medium">Password Management</h3>
                    <div className="grid gap-4">
                      <Button variant="outline" className="w-full justify-start">
                        <Lock className="w-4 h-4 mr-2" />
                        Change Password
                      </Button>
                      <div className="text-sm text-muted-foreground">
                        Last changed: {securitySettings.passwordLastChanged}
                      </div>
                    </div>
                  </div>

                  {/* Session Management */}
                  <div className="space-y-4">
                    <h3 className="font-medium">Session Management</h3>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span>Session Timeout</span>
                        <span>{securitySettings.sessionTimeout} minutes</span>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span>Last Login</span>
                        <span>{securitySettings.lastLogin}</span>
                      </div>
                    </div>
                  </div>

                  {/* Login History */}
                  <div className="space-y-4">
                    <h3 className="font-medium">Recent Login Activity</h3>
                    <div className="space-y-3">
                      {securitySettings.loginHistory.map((login, index) => (
                        <div key={index} className="flex items-center justify-between p-3 bg-muted rounded-lg">
                          <div className="space-y-1">
                            <div className="text-sm font-medium">{login.device}</div>
                            <div className="text-xs text-muted-foreground">{login.location}</div>
                          </div>
                          <div className="text-xs text-muted-foreground">{login.date}</div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Danger Zone */}
                  <div className="pt-6 border-t border-border">
                    <h3 className="font-medium text-destructive mb-4">Danger Zone</h3>
                    <div className="space-y-3">
                      <Button variant="outline" className="w-full justify-start text-destructive hover:text-destructive">
                        <Download className="w-4 h-4 mr-2" />
                        Download Account Data
                      </Button>
                      <Button variant="outline" className="w-full justify-start text-destructive hover:text-destructive">
                        <Trash2 className="w-4 h-4 mr-2" />
                        Delete Account
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </main>
    </div>
  );
};

export default SettingsPage; 