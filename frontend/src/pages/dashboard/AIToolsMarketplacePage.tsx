import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  Bot, 
  MessageSquare, 
  Mail, 
  Smartphone, 
  Calendar, 
  TrendingUp, 
  ShoppingCart, 
  Users, 
  Search,
  Star,
  Zap,
  Clock,
  DollarSign,
  CheckCircle,
  ArrowRight,
  Sparkles
} from "lucide-react";
import { Link } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";

interface AITool {
  id: string;
  name: string;
  description: string;
  category: 'communication' | 'automation' | 'analytics' | 'templates';
  icon: React.ComponentType;
  features: string[];
  pricing: {
    type: 'free' | 'included' | 'addon';
    price?: string;
    billingCycle?: string;
  };
  minimumPlan: 'STARTER' | 'PROFESSIONAL' | 'ENTERPRISE';
  popularity: number;
  isNew?: boolean;
  estimatedSetupTime: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  industryFocus?: string[];
}

const aiTools: AITool[] = [
  {
    id: 'multi-agent-chatbot',
    name: 'Multi-Agent Chatbot',
    description: 'Intelligent chatbot with specialized agents for different customer needs',
    category: 'communication',
    icon: Bot,
    features: ['Agent Routing', 'Context Memory', 'Multi-Channel', '24/7 Support'],
    pricing: { type: 'included' },
    minimumPlan: 'STARTER',
    popularity: 95,
    estimatedSetupTime: '15 minutes',
    difficulty: 'beginner',
    industryFocus: ['All Industries']
  },
  {
    id: 'whatsapp-automation',
    name: 'WhatsApp Business AI',
    description: 'Automate WhatsApp conversations with rich media and broadcast messaging',
    category: 'communication',
    icon: MessageSquare,
    features: ['Rich Media', 'Broadcast Messages', 'Template Management', 'Opt-in Management'],
    pricing: { type: 'included' },
    minimumPlan: 'PROFESSIONAL',
    popularity: 78,
    estimatedSetupTime: '30 minutes',
    difficulty: 'intermediate',
    industryFocus: ['Retail', 'Restaurant', 'Services']
  },
  {
    id: 'email-response-ai',
    name: 'Email Response AI',
    description: 'Automatically draft and send personalized email responses',
    category: 'communication',
    icon: Mail,
    features: ['Auto-Response', 'Sentiment Analysis', 'Template Library', 'A/B Testing'],
    pricing: { type: 'included' },
    minimumPlan: 'PROFESSIONAL',
    popularity: 65,
    isNew: true,
    estimatedSetupTime: '45 minutes',
    difficulty: 'intermediate',
    industryFocus: ['B2B', 'Professional Services']
  },
  {
    id: 'sms-marketing-ai',
    name: 'SMS Marketing AI',
    description: 'Intelligent SMS campaigns with personalized messaging',
    category: 'communication',
    icon: Smartphone,
    features: ['Campaign Automation', 'Personalization', 'Compliance Tracking', 'Analytics'],
    pricing: { type: 'addon', price: '$29', billingCycle: 'month' },
    minimumPlan: 'PROFESSIONAL',
    popularity: 42,
    estimatedSetupTime: '20 minutes',
    difficulty: 'beginner',
    industryFocus: ['Retail', 'Healthcare', 'Automotive']
  },
  {
    id: 'appointment-booking',
    name: 'Smart Appointment Booking',
    description: 'AI-powered scheduling with calendar integration and reminders',
    category: 'automation',
    icon: Calendar,
    features: ['Calendar Sync', 'Automated Reminders', 'Availability Management', 'Rescheduling'],
    pricing: { type: 'included' },
    minimumPlan: 'PROFESSIONAL',
    popularity: 85,
    estimatedSetupTime: '25 minutes',
    difficulty: 'intermediate',
    industryFocus: ['Healthcare', 'Professional Services', 'Beauty']
  },
  {
    id: 'lead-qualification',
    name: 'Lead Qualification AI',
    description: 'Automatically qualify and score leads based on conversation data',
    category: 'automation',
    icon: Users,
    features: ['Lead Scoring', 'Qualification Rules', 'CRM Integration', 'Follow-up Automation'],
    pricing: { type: 'included' },
    minimumPlan: 'PROFESSIONAL',
    popularity: 72,
    estimatedSetupTime: '40 minutes',
    difficulty: 'advanced',
    industryFocus: ['B2B', 'Real Estate', 'Financial Services']
  },
  {
    id: 'order-management',
    name: 'Order Management AI',
    description: 'Handle order inquiries, modifications, and tracking automatically',
    category: 'automation',
    icon: ShoppingCart,
    features: ['Order Tracking', 'Modification Handling', 'Return Processing', 'Inventory Sync'],
    pricing: { type: 'included' },
    minimumPlan: 'PROFESSIONAL',
    popularity: 68,
    estimatedSetupTime: '35 minutes',
    difficulty: 'intermediate',
    industryFocus: ['E-commerce', 'Retail', 'Manufacturing']
  },
  {
    id: 'customer-analytics',
    name: 'Customer Analytics AI',
    description: 'Deep insights into customer behavior and satisfaction patterns',
    category: 'analytics',
    icon: TrendingUp,
    features: ['Behavior Analysis', 'Satisfaction Tracking', 'Churn Prediction', 'Segment Insights'],
    pricing: { type: 'included' },
    minimumPlan: 'ENTERPRISE',
    popularity: 59,
    estimatedSetupTime: '60 minutes',
    difficulty: 'advanced',
    industryFocus: ['All Industries']
  }
];

const AIToolsMarketplacePage = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedPlan, setSelectedPlan] = useState<string>('all');
  const [filteredTools, setFilteredTools] = useState<AITool[]>(aiTools);
  const { currentBusiness } = useAuth();

  useEffect(() => {
    let filtered = aiTools;

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(tool => 
        tool.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        tool.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        tool.features.some(feature => feature.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // Filter by category
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(tool => tool.category === selectedCategory);
    }

    // Filter by plan
    if (selectedPlan !== 'all') {
      const planHierarchy = { 'STARTER': 1, 'PROFESSIONAL': 2, 'ENTERPRISE': 3 };
      const selectedPlanLevel = planHierarchy[selectedPlan as keyof typeof planHierarchy];
      filtered = filtered.filter(tool => {
        const toolPlanLevel = planHierarchy[tool.minimumPlan];
        return toolPlanLevel <= selectedPlanLevel;
      });
    }

    // Sort by popularity
    filtered.sort((a, b) => b.popularity - a.popularity);

    setFilteredTools(filtered);
  }, [searchTerm, selectedCategory, selectedPlan]);

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'communication': return MessageSquare;
      case 'automation': return Zap;
      case 'analytics': return TrendingUp;
      case 'templates': return Sparkles;
      default: return Bot;
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'bg-green-100 text-green-800';
      case 'intermediate': return 'bg-yellow-100 text-yellow-800';
      case 'advanced': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPlanColor = (plan: string) => {
    switch (plan) {
      case 'STARTER': return 'bg-blue-100 text-blue-800';
      case 'PROFESSIONAL': return 'bg-purple-100 text-purple-800';
      case 'ENTERPRISE': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <Sparkles className="h-8 w-8 text-blue-600" />
            AI Tools Marketplace
          </h1>
          <p className="text-muted-foreground mt-2">
            Discover and deploy AI solutions to transform your customer experience
          </p>
        </div>
        <div className="flex items-center gap-4">
          <Link to="/dashboard/ai-tools/custom">
            <Button variant="outline">
              <Bot className="h-4 w-4 mr-2" />
              Request Custom AI Tool
            </Button>
          </Link>
          <Link to="/dashboard">
            <Button variant="outline">Back to Dashboard</Button>
          </Link>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Available Tools</p>
                <p className="text-2xl font-bold">{aiTools.length}</p>
              </div>
              <Bot className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Deployed Tools</p>
                <p className="text-2xl font-bold">3</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Setup Time Saved</p>
                <p className="text-2xl font-bold">4.5h</p>
              </div>
              <Clock className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Monthly Savings</p>
                <p className="text-2xl font-bold">$2,400</p>
              </div>
              <DollarSign className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-wrap items-center gap-4">
            <div className="flex-1 min-w-[200px]">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search AI tools..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                <SelectItem value="communication">Communication</SelectItem>
                <SelectItem value="automation">Automation</SelectItem>
                <SelectItem value="analytics">Analytics</SelectItem>
                <SelectItem value="templates">Templates</SelectItem>
              </SelectContent>
            </Select>
            <Select value={selectedPlan} onValueChange={setSelectedPlan}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Plan Access" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Plans</SelectItem>
                <SelectItem value="STARTER">Starter & Above</SelectItem>
                <SelectItem value="PROFESSIONAL">Professional & Above</SelectItem>
                <SelectItem value="ENTERPRISE">Enterprise Only</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* AI Tools Grid */}
      <Tabs value={selectedCategory} onValueChange={setSelectedCategory} className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="all">All Tools</TabsTrigger>
          <TabsTrigger value="communication">Communication</TabsTrigger>
          <TabsTrigger value="automation">Automation</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="templates">Templates</TabsTrigger>
        </TabsList>

        <TabsContent value={selectedCategory} className="mt-6">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredTools.map((tool) => {
              const IconComponent = tool.icon;
              return (
                <Card key={tool.id} className="relative hover:shadow-lg transition-shadow">
                  {tool.isNew && (
                    <Badge className="absolute -top-2 -right-2 bg-green-500">
                      New
                    </Badge>
                  )}
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-3">
                        <div className="p-2 bg-blue-100 rounded-lg">
                          <IconComponent className="h-6 w-6 text-blue-600" />
                        </div>
                        <div>
                          <CardTitle className="text-lg">{tool.name}</CardTitle>
                          <div className="flex items-center gap-2 mt-1">
                            <div className="flex items-center">
                              {[...Array(5)].map((_, i) => (
                                <Star 
                                  key={i} 
                                  className={`h-3 w-3 ${i < Math.floor(tool.popularity / 20) ? 'text-yellow-400 fill-current' : 'text-gray-300'}`} 
                                />
                              ))}
                            </div>
                            <span className="text-xs text-muted-foreground">
                              {tool.popularity}% adoption
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                    <CardDescription className="mt-2">
                      {tool.description}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {/* Features */}
                      <div>
                        <p className="text-sm font-medium mb-2">Key Features:</p>
                        <div className="flex flex-wrap gap-1">
                          {tool.features.slice(0, 3).map((feature) => (
                            <Badge key={feature} variant="secondary" className="text-xs">
                              {feature}
                            </Badge>
                          ))}
                          {tool.features.length > 3 && (
                            <Badge variant="secondary" className="text-xs">
                              +{tool.features.length - 3} more
                            </Badge>
                          )}
                        </div>
                      </div>

                      {/* Metadata */}
                      <div className="flex flex-wrap gap-2 text-xs">
                        <Badge className={getDifficultyColor(tool.difficulty)}>
                          {tool.difficulty}
                        </Badge>
                        <Badge className={getPlanColor(tool.minimumPlan)}>
                          {tool.minimumPlan}+ Plan
                        </Badge>
                        <Badge variant="outline">
                          <Clock className="h-3 w-3 mr-1" />
                          {tool.estimatedSetupTime}
                        </Badge>
                      </div>

                      {/* Pricing */}
                      <div className="flex items-center justify-between">
                        <div>
                          {tool.pricing.type === 'included' && (
                            <span className="text-green-600 font-medium">Included</span>
                          )}
                          {tool.pricing.type === 'free' && (
                            <span className="text-blue-600 font-medium">Free</span>
                          )}
                          {tool.pricing.type === 'addon' && (
                            <span className="font-medium">
                              {tool.pricing.price}/{tool.pricing.billingCycle}
                            </span>
                          )}
                        </div>
                        <Link to={`/dashboard/ai-tools/${tool.id}/setup`}>
                          <Button size="sm">
                            Deploy
                            <ArrowRight className="h-4 w-4 ml-1" />
                          </Button>
                        </Link>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>

          {filteredTools.length === 0 && (
            <div className="text-center py-12">
              <Bot className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">No tools found</h3>
              <p className="text-muted-foreground">
                Try adjusting your search or filter criteria
              </p>
            </div>
          )}
        </TabsContent>
      </Tabs>

      {/* Call to Action */}
      <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-xl font-bold mb-2">Need a Custom AI Solution?</h3>
              <p className="text-muted-foreground">
                Our AI experts can build custom tools tailored to your specific business needs.
              </p>
            </div>
            <Link to="/dashboard/ai-tools/custom-request">
              <Button>
                <Sparkles className="h-4 w-4 mr-2" />
                Request Custom Tool
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AIToolsMarketplacePage; 