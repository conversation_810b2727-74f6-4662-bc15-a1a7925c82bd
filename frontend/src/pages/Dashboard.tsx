import { useState, useEffect } from "react";
import { <PERSON> } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Bot, Plus, TrendingUp, MessageCircle, Settings, HelpCircle, Sparkles, Loader2, Activity } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { agentsService } from "@/services/agentsService";
import { analyticsService } from "@/services/analyticsService";
import { UserMenu } from "@/components/UserMenu";
import type { AgentConfig, ActivityItem } from "@/types/api";

const Dashboard = () => {
  const { user, currentBusiness, isLoading } = useAuth();
  const [agents, setAgents] = useState<AgentConfig[]>([]);
  const [recentActivity, setRecentActivity] = useState<ActivityItem[]>([]);
  const [loadingAgents, setLoadingAgents] = useState(false);
  const [loadingActivity, setLoadingActivity] = useState(false);
  const [showOnboarding, setShowOnboarding] = useState(false);

  // Check if user is new (no businesses) or has business but no agents
  const isNewUser = !currentBusiness && !isLoading;
  const hasBusinessButNoAgents = currentBusiness && agents.length === 0 && !loadingAgents;

  useEffect(() => {
    if (currentBusiness) {
      loadAgents();
      loadRecentActivity();
    }
  }, [currentBusiness]);

  const loadAgents = async () => {
    if (!currentBusiness) return;
    
    try {
      setLoadingAgents(true);
      const agentsData = await agentsService.getAgents(currentBusiness.id);
      setAgents(agentsData);
    } catch (error) {
      console.error('Failed to load agents:', error);
    } finally {
      setLoadingAgents(false);
    }
  };

  const loadRecentActivity = async () => {
    if (!currentBusiness) return;
    
    try {
      setLoadingActivity(true);
      const activityData = await analyticsService.getRecentActivity(currentBusiness.id);
      setRecentActivity(activityData);
    } catch (error) {
      console.error('Failed to load recent activity:', error);
    } finally {
      setLoadingActivity(false);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-subtle flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="relative">
            <div className="w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center mx-auto">
              <Bot className="w-8 h-8 text-white" />
            </div>
            <div className="absolute -top-1 -right-1 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
              <Loader2 className="w-4 h-4 text-white animate-spin" />
            </div>
          </div>
          <div className="space-y-2">
            <h2 className="text-xl font-semibold text-foreground">Loading your dashboard...</h2>
            <p className="text-muted-foreground">Setting up your AI workspace</p>
          </div>
        </div>
      </div>
    );
  }

  // Enhanced Welcome Empty State for New Users (no businesses)
  if (isNewUser) {
    return (
      <div className="min-h-screen bg-gradient-subtle">
        <header className="border-b border-border bg-background/80 backdrop-blur-sm sticky top-0 z-50">
          <div className="container mx-auto px-4 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center">
                  <Bot className="w-5 h-5 text-white" />
                </div>
                <h1 className="text-xl font-bold text-foreground">PalAI</h1>
              </div>
              <UserMenu />
            </div>
          </div>
        </header>

        <main className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto space-y-12">
            {/* Hero Section */}
            <div className="text-center space-y-6">
              <div className="relative">
                <div className="w-32 h-32 bg-gradient-primary rounded-full flex items-center justify-center mx-auto">
                  <Bot className="w-16 h-16 text-white" />
                </div>
                <div className="absolute -top-4 -right-4 w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center">
                  <Sparkles className="w-7 h-7 text-white" />
                </div>
              </div>
              <div className="space-y-4">
                <h1 className="text-5xl lg:text-6xl font-bold text-foreground">
                  Welcome to PalAI! 🎉
                </h1>
                <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
                  Transform your business with intelligent AI agents that provide exceptional customer experiences
                </p>
              </div>
            </div>

            {/* Quick Start Cards */}
            <div className="grid md:grid-cols-3 gap-6">
              <Card className="border-0 shadow-soft hover:shadow-elegant transition-all duration-300 hover:-translate-y-1 bg-card">
                <CardHeader className="text-center pb-4">
                  <div className="w-16 h-16 bg-gradient-primary rounded-lg flex items-center justify-center mx-auto mb-4">
                    <Plus className="w-8 h-8 text-white" />
                  </div>
                  <CardTitle className="text-xl">Create Business</CardTitle>
                  <CardDescription>
                    Set up your business profile and get started with AI-powered customer engagement
                  </CardDescription>
                </CardHeader>
                <CardContent className="text-center">
                  <Link to="/create-business">
                    <Button className="w-full bg-gradient-primary hover:opacity-90">
                      Get Started
                    </Button>
                  </Link>
                </CardContent>
              </Card>

              <Card className="border-0 shadow-soft hover:shadow-elegant transition-all duration-300 hover:-translate-y-1 bg-card">
                <CardHeader className="text-center pb-4">
                  <div className="w-16 h-16 bg-green-500 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <Bot className="w-8 h-8 text-white" />
                  </div>
                  <CardTitle className="text-xl">Configure AI</CardTitle>
                  <CardDescription>
                    Design intelligent agents tailored to your business needs and customer interactions
                  </CardDescription>
                </CardHeader>
                <CardContent className="text-center">
                  <Button variant="outline" className="w-full" disabled>
                    Coming Soon
                  </Button>
                </CardContent>
              </Card>

              <Card className="border-0 shadow-soft hover:shadow-elegant transition-all duration-300 hover:-translate-y-1 bg-card">
                <CardHeader className="text-center pb-4">
                  <div className="w-16 h-16 bg-blue-500 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <TrendingUp className="w-8 h-8 text-white" />
                  </div>
                  <CardTitle className="text-xl">Deploy & Scale</CardTitle>
                  <CardDescription>
                    Launch your AI agents across multiple channels and monitor their performance
                  </CardDescription>
                </CardHeader>
                <CardContent className="text-center">
                  <Button variant="outline" className="w-full" disabled>
                    Coming Soon
                  </Button>
                </CardContent>
              </Card>
            </div>

            {/* CTA Section */}
            <div className="text-center space-y-6">
              <div className="space-y-4">
                <h2 className="text-3xl font-bold text-foreground">Ready to get started?</h2>
                <p className="text-lg text-muted-foreground">
                  Create your first business and start building intelligent AI agents today
                </p>
              </div>
              <Link to="/create-business">
                <Button size="lg" className="bg-gradient-primary hover:opacity-90 text-lg px-8 py-6">
                  <Plus className="w-6 h-6 mr-3" />
                  Create Your First Business
                </Button>
              </Link>
            </div>

            {/* Help Resources */}
            <div className="grid md:grid-cols-3 gap-4">
              <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
                <HelpCircle className="w-6 h-6" />
                <span>Documentation</span>
              </Button>
              <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
                <MessageCircle className="w-6 h-6" />
                <span>Video Tutorials</span>
              </Button>
              <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
                <Activity className="w-6 h-6" />
                <span>Live Chat</span>
              </Button>
            </div>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-subtle">
      {/* Header */}
      <header className="border-b border-border bg-background/80 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center">
                <Bot className="w-5 h-5 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-foreground">PalAI</h1>
                {currentBusiness && (
                  <p className="text-sm text-muted-foreground">{currentBusiness.name}</p>
                )}
              </div>
            </div>
            <div className="flex items-center gap-4">
              <Link to="/select-business">
                <Button variant="outline">Switch Business</Button>
              </Link>
              <UserMenu />
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        <div className="space-y-8">
          {/* Welcome Section */}
          <div className="text-center space-y-4">
            <h1 className="text-4xl lg:text-5xl font-bold text-foreground">
              Welcome back, {user?.firstName}! 👋
            </h1>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Manage your AI agents and monitor their performance
            </p>
          </div>

          {/* Quick Actions Panel */}
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Link to="/dashboard/agents/new">
              <Card className="border-0 shadow-soft hover:shadow-elegant transition-all duration-300 hover:-translate-y-1 bg-card cursor-pointer">
                <CardHeader className="text-center pb-4">
                  <div className="w-12 h-12 bg-gradient-primary rounded-lg flex items-center justify-center mx-auto mb-4">
                    <Plus className="w-6 h-6 text-white" />
                  </div>
                  <CardTitle className="text-lg">Create Agent</CardTitle>
                  <CardDescription>
                    Build a new AI assistant
                  </CardDescription>
                </CardHeader>
              </Card>
            </Link>

            <Link to="/dashboard/analytics">
              <Card className="border-0 shadow-soft hover:shadow-elegant transition-all duration-300 hover:-translate-y-1 bg-card cursor-pointer">
                <CardHeader className="text-center pb-4">
                  <div className="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <TrendingUp className="w-6 h-6 text-white" />
                  </div>
                  <CardTitle className="text-lg">Analytics</CardTitle>
                  <CardDescription>
                    View performance metrics
                  </CardDescription>
                </CardHeader>
              </Card>
            </Link>

            <Link to="/dashboard/chat">
              <Card className="border-0 shadow-soft hover:shadow-elegant transition-all duration-300 hover:-translate-y-1 bg-card cursor-pointer">
                <CardHeader className="text-center pb-4">
                  <div className="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <MessageCircle className="w-6 h-6 text-white" />
                  </div>
                  <CardTitle className="text-lg">Test Chat</CardTitle>
                  <CardDescription>
                    Test your AI agents
                  </CardDescription>
                </CardHeader>
              </Card>
            </Link>

            <Link to="/dashboard/settings">
              <Card className="border-0 shadow-soft hover:shadow-elegant transition-all duration-300 hover:-translate-y-1 bg-card cursor-pointer">
                <CardHeader className="text-center pb-4">
                  <div className="w-12 h-12 bg-purple-500 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <Settings className="w-6 h-6 text-white" />
                  </div>
                  <CardTitle className="text-lg">Settings</CardTitle>
                  <CardDescription>
                    Configure preferences
                  </CardDescription>
                </CardHeader>
              </Card>
            </Link>
          </div>

          {/* Enhanced Empty State for No Agents */}
          {hasBusinessButNoAgents && (
            <Card className="border-0 shadow-soft bg-card">
              <CardContent className="text-center py-16">
                <div className="space-y-6">
                  <div className="relative">
                    <div className="w-24 h-24 bg-gradient-primary rounded-full flex items-center justify-center mx-auto">
                      <Bot className="w-12 h-12 text-white" />
                    </div>
                    <div className="absolute -top-2 -right-2 w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                      <Sparkles className="w-5 h-5 text-white" />
                    </div>
                  </div>
                  <div className="space-y-4">
                    <h3 className="text-2xl font-bold text-foreground">Ready to create your first AI agent?</h3>
                    <p className="text-muted-foreground max-w-md mx-auto">
                      Your business is set up! Now let's create an intelligent AI assistant to help your customers.
                    </p>
                  </div>
                  <div className="flex flex-col sm:flex-row gap-4 justify-center">
                    <Link to="/dashboard/agents/new">
                      <Button size="lg" className="bg-gradient-primary hover:opacity-90">
                        <Plus className="w-5 h-5 mr-2" />
                        Create Your First Agent
                      </Button>
                    </Link>
                    <Button variant="outline" size="lg">
                      <HelpCircle className="w-5 h-5 mr-2" />
                      View Templates
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Recent Activity and Agents */}
          {!hasBusinessButNoAgents && (
            <div className="grid lg:grid-cols-2 gap-8">
              {/* Recent Activity */}
              <div className="space-y-6">
                <h2 className="text-2xl font-bold text-foreground">Recent Activity</h2>
                <Card className="border-0 shadow-soft bg-card">
                  <CardHeader>
                    <CardTitle>Latest Events</CardTitle>
                    <CardDescription>Recent actions and conversations</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {loadingActivity ? (
                      <div className="text-center py-8">
                        <Loader2 className="w-8 h-8 text-muted-foreground mx-auto mb-2 animate-spin" />
                        <p className="text-sm text-muted-foreground">Loading activity...</p>
                      </div>
                    ) : recentActivity.length > 0 ? (
                      <div className="space-y-4">
                        {recentActivity.slice(0, 5).map((activity) => (
                          <div key={activity.id} className="flex items-start space-x-3">
                            <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                            <div className="flex-1 min-w-0">
                              <p className="text-sm font-medium text-foreground">
                                {activity.action}
                              </p>
                              {activity.agent && activity.agent !== "-" && (
                                <p className="text-xs text-muted-foreground">
                                  Agent: {activity.agent}
                                </p>
                              )}
                              {activity.details && (
                                <p className="text-xs text-muted-foreground">
                                  {activity.details}
                                </p>
                              )}
                            </div>
                            <div className="text-xs text-muted-foreground whitespace-nowrap">
                              {activity.time}
                            </div>
                          </div>
                        ))}
                        <div className="pt-2 border-t border-border">
                          <Link to="/dashboard/analytics">
                            <Button variant="outline" size="sm" className="w-full">
                              View All Activity
                            </Button>
                          </Link>
                        </div>
                      </div>
                    ) : (
                      <div className="text-center py-8">
                        <Activity className="w-8 h-8 text-muted-foreground mx-auto mb-2" />
                        <p className="text-sm text-muted-foreground">No recent activity</p>
                        <p className="text-xs text-muted-foreground">
                          Activity will appear here as your agents interact with customers
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>

              {/* Your Agents */}
              <div className="space-y-6">
                <h2 className="text-2xl font-bold text-foreground">Your Agents</h2>
                <Card className="border-0 shadow-soft bg-card">
                  <CardHeader>
                    <CardTitle>AI Chat Agents</CardTitle>
                    <CardDescription>Manage and monitor your AI chat agents</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {loadingAgents ? (
                      <div className="text-center py-12">
                        <div className="relative">
                          <div className="w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-4">
                            <Bot className="w-8 h-8 text-white" />
                          </div>
                          <div className="absolute -top-1 -right-1 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                            <Loader2 className="w-4 h-4 text-white animate-spin" />
                          </div>
                        </div>
                        <h3 className="text-lg font-semibold text-foreground mb-2">Loading your agents...</h3>
                        <p className="text-muted-foreground">
                          Please wait while we fetch your AI agents
                        </p>
                      </div>
                    ) : agents.length === 0 ? (
                      <div className="text-center py-12">
                        <Bot className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
                        <h3 className="text-lg font-semibold text-foreground mb-2">No agents yet</h3>
                        <p className="text-muted-foreground mb-6">
                          Get started by creating your first AI chat agent
                        </p>
                        <Link to="/dashboard/agents/new">
                          <Button className="bg-gradient-primary hover:opacity-90">
                            <Plus className="w-4 h-4 mr-2" />
                            Create Your First Agent
                          </Button>
                        </Link>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-muted-foreground">
                            {agents.length} agent{agents.length !== 1 ? 's' : ''} active
                          </span>
                          <Link to="/dashboard/agents">
                            <Button variant="outline" size="sm">
                              View All
                            </Button>
                          </Link>
                        </div>
                        <div className="grid gap-4">
                          {agents.slice(0, 3).map((agent) => (
                            <div key={agent.id} className="flex items-center justify-between p-4 bg-muted rounded-lg">
                              <div className="flex items-center space-x-3">
                                <div className="w-10 h-10 bg-gradient-primary rounded-lg flex items-center justify-center">
                                  <Bot className="w-5 h-5 text-white" />
                                </div>
                                <div>
                                  <h4 className="font-semibold text-foreground">{agent.name}</h4>
                                  <p className="text-sm text-muted-foreground">
                                    {agent.isActive ? 'Active' : 'Inactive'} • {agent.conversationsToday || 0} conversations today
                                  </p>
                                </div>
                              </div>
                              <div className="flex items-center space-x-2">
                                <div className={`w-3 h-3 rounded-full ${agent.isActive ? 'bg-green-500' : 'bg-gray-400'}`}></div>
                                <span className="text-xs text-muted-foreground">
                                  {agent.averageResponseTimeMs ? `${agent.averageResponseTimeMs}ms` : '0ms'}
                                </span>
                              </div>
                            </div>
                          ))}
                        </div>
                        {agents.length > 3 && (
                          <div className="text-center pt-4">
                            <Link to="/dashboard/agents">
                              <Button variant="outline">
                                View {agents.length - 3} More Agent{agents.length - 3 !== 1 ? 's' : ''}
                              </Button>
                            </Link>
                          </div>
                        )}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </div>
          )}
        </div>
      </main>
    </div>
  );
};

export default Dashboard;