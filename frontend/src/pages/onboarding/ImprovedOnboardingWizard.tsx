import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { Badge } from '../../components/ui/badge';
import { Progress } from '../../components/ui/progress';
import { CheckCircle, ArrowRight, Sparkles, Bot, MessageSquare, Calendar, TrendingUp } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import type { CreateBusinessRequest } from '../../services/onboardingService';

// Improved onboarding flow: Discovery → Trial → Configuration → Commitment
const ImprovedOnboardingWizard: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [selectedAITools, setSelectedAITools] = useState<string[]>(['multi-agent-chatbot']);
  const [businessData, setBusinessData] = useState<CreateBusinessRequest>({
    name: '',
    email: '',
    phone: '',
    website: '',
    industry: '',
    plan: 'TRIAL', // Start with trial
    chatbotConfig: {
      welcomeMessage: '',
      agentName: '',
      agentType: 'GENERAL',
      agentPersonality: 'helpful, friendly, professional'
    },
    widgetConfig: {
      primaryColor: '#007bff',
      secondaryColor: '#6c757d',
      position: 'bottom-right',
      theme: 'light',
      showGreeting: true,
      greetingDelay: 3,
      enableSounds: true,
      enableAnimations: true
    }
  });

  const navigate = useNavigate();

  const improvedSteps = [
    {
      id: 1,
      title: 'AI Discovery',
      description: 'Explore AI solutions for your business',
      component: 'AIDiscoveryStep'
    },
    {
      id: 2,
      title: 'Business Profile',
      description: 'Tell us about your business',
      component: 'BusinessInfoStep'
    },
    {
      id: 3,
      title: 'AI Configuration',
      description: 'Customize your selected AI tools',
      component: 'AIConfigurationStep'
    },
    {
      id: 4,
      title: 'Free Trial Setup',
      description: 'Deploy and test your AI solution',
      component: 'TrialSetupStep'
    },
    {
      id: 5,
      title: 'Plan Selection',
      description: 'Choose your subscription after trial',
      component: 'PlanSelectionStep'
    }
  ];

  const aiToolOptions = [
    {
      id: 'multi-agent-chatbot',
      name: 'Smart Chatbot',
      description: 'Intelligent multi-agent chatbot with routing',
      icon: Bot,
      included: true,
      timeToValue: '5 minutes',
      impact: 'Reduce support tickets by 60%'
    },
    {
      id: 'whatsapp-automation',
      name: 'WhatsApp AI',
      description: 'Automate WhatsApp business conversations',
      icon: MessageSquare,
      included: false,
      timeToValue: '15 minutes',
      impact: 'Increase engagement by 3x'
    },
    {
      id: 'appointment-booking',
      name: 'Smart Scheduling',
      description: 'AI-powered appointment booking system',
      icon: Calendar,
      included: false,
      timeToValue: '10 minutes',
      impact: 'Save 5 hours/week on scheduling'
    },
    {
      id: 'customer-analytics',
      name: 'AI Analytics',
      description: 'Deep customer behavior insights',
      icon: TrendingUp,
      included: false,
      timeToValue: '20 minutes',
      impact: 'Improve satisfaction by 25%'
    }
  ];

  const currentStepData = improvedSteps[currentStep - 1];
  const progress = (currentStep / improvedSteps.length) * 100;

  const handleNext = () => {
    if (currentStep < improvedSteps.length) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const toggleAITool = (toolId: string) => {
    if (toolId === 'multi-agent-chatbot') return; // Always included
    
    setSelectedAITools(prev => 
      prev.includes(toolId)
        ? prev.filter(id => id !== toolId)
        : [...prev, toolId]
    );
  };

  const renderAIDiscoveryStep = () => (
    <div className="space-y-6">
      <div className="text-center space-y-4">
        <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto">
          <Sparkles className="w-8 h-8 text-white" />
        </div>
        <h2 className="text-2xl font-bold">Welcome to PalAI!</h2>
        <p className="text-muted-foreground max-w-2xl mx-auto">
          Choose AI solutions that will transform your customer experience. Each tool is designed to save time and increase customer satisfaction.
        </p>
      </div>

      <div className="grid md:grid-cols-2 gap-4">
        {aiToolOptions.map((tool) => {
          const IconComponent = tool.icon;
          const isSelected = selectedAITools.includes(tool.id);
          const isRequired = tool.id === 'multi-agent-chatbot';
          
          return (
            <Card 
              key={tool.id} 
              className={`cursor-pointer transition-all border-2 ${
                isSelected 
                  ? 'border-blue-500 bg-blue-50' 
                  : 'border-gray-200 hover:border-gray-300'
              } ${isRequired ? 'opacity-100' : ''}`}
              onClick={() => !isRequired && toggleAITool(tool.id)}
            >
              <CardContent className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <div className={`p-2 rounded-lg ${isSelected ? 'bg-blue-100' : 'bg-gray-100'}`}>
                      <IconComponent className={`w-6 h-6 ${isSelected ? 'text-blue-600' : 'text-gray-600'}`} />
                    </div>
                    <div>
                      <h3 className="font-semibold flex items-center gap-2">
                        {tool.name}
                        {isRequired && <Badge variant="secondary">Included</Badge>}
                        {isSelected && !isRequired && <CheckCircle className="w-4 h-4 text-blue-600" />}
                      </h3>
                      <p className="text-sm text-muted-foreground">{tool.description}</p>
                    </div>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Setup time:</span>
                    <span className="font-medium">{tool.timeToValue}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Expected impact:</span>
                    <span className="font-medium text-green-600">{tool.impact}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      <div className="bg-gradient-to-r from-green-50 to-blue-50 p-6 rounded-lg border border-green-200">
        <h3 className="font-semibold text-green-800 mb-2">🎉 Free 14-Day Trial</h3>
        <p className="text-green-700 mb-4">
          Test all selected AI tools with real customers. No credit card required. Cancel anytime.
        </p>
        <div className="flex items-center gap-4 text-sm text-green-600">
          <span>✓ Full feature access</span>
          <span>✓ Unlimited conversations</span>
          <span>✓ Expert support</span>
        </div>
      </div>
    </div>
  );

  const renderTrialSetupStep = () => (
    <div className="space-y-6">
      <div className="text-center space-y-4">
        <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-blue-600 rounded-full flex items-center justify-center mx-auto">
          <CheckCircle className="w-8 h-8 text-white" />
        </div>
        <h2 className="text-2xl font-bold">Your AI Solution is Ready!</h2>
        <p className="text-muted-foreground max-w-2xl mx-auto">
          We've configured your selected AI tools. Your 14-day free trial starts now.
        </p>
      </div>

      <div className="grid gap-4">
        <Card className="border-green-200 bg-green-50">
          <CardContent className="p-6">
            <h3 className="font-semibold text-green-800 mb-4">What happens next:</h3>
            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <CheckCircle className="w-5 h-5 text-green-600" />
                <span>Instant access to your AI dashboard</span>
              </div>
              <div className="flex items-center gap-3">
                <CheckCircle className="w-5 h-5 text-green-600" />
                <span>Widget embed code for your website</span>
              </div>
              <div className="flex items-center gap-3">
                <CheckCircle className="w-5 h-5 text-green-600" />
                <span>Real-time performance analytics</span>
              </div>
              <div className="flex items-center gap-3">
                <CheckCircle className="w-5 h-5 text-green-600" />
                <span>Expert onboarding support</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <h3 className="font-semibold mb-4">Selected AI Tools ({selectedAITools.length})</h3>
            <div className="grid md:grid-cols-2 gap-3">
              {selectedAITools.map(toolId => {
                const tool = aiToolOptions.find(t => t.id === toolId);
                if (!tool) return null;
                const IconComponent = tool.icon;
                return (
                  <div key={toolId} className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
                    <IconComponent className="w-5 h-5 text-blue-600" />
                    <span className="font-medium">{tool.name}</span>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
        <p className="text-sm text-yellow-800">
          <strong>Reminder:</strong> Your trial includes full access to all features. 
          We'll remind you 3 days before it expires to choose a plan.
        </p>
      </div>
    </div>
  );

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 1:
        return renderAIDiscoveryStep();
      case 2:
        return <div>Business Info Step (existing component)</div>;
      case 3:
        return <div>AI Configuration Step</div>;
      case 4:
        return renderTrialSetupStep();
      case 5:
        return <div>Plan Selection Step (moved to end)</div>;
      default:
        return <div>Unknown step</div>;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50">
      <div className="container mx-auto px-4 py-8">
        {/* Progress Header */}
        <div className="max-w-4xl mx-auto mb-8">
          <div className="flex items-center justify-between mb-4">
            <h1 className="text-xl font-semibold">Business Setup</h1>
            <div className="text-sm text-muted-foreground">
              Step {currentStep} of {improvedSteps.length}
            </div>
          </div>
          <Progress value={progress} className="h-2" />
          <div className="mt-2">
            <p className="font-medium">{currentStepData.title}</p>
            <p className="text-sm text-muted-foreground">{currentStepData.description}</p>
          </div>
        </div>

        {/* Step Content */}
        <div className="max-w-4xl mx-auto">
          <Card>
            <CardContent className="p-8">
              {renderCurrentStep()}
            </CardContent>
          </Card>
        </div>

        {/* Navigation */}
        <div className="max-w-4xl mx-auto mt-8 flex justify-between">
          <Button 
            variant="outline" 
            onClick={handleBack} 
            disabled={currentStep === 1}
          >
            Back
          </Button>
          <Button onClick={handleNext} disabled={currentStep === improvedSteps.length}>
            {currentStep === improvedSteps.length ? 'Complete Setup' : 'Continue'}
            <ArrowRight className="w-4 h-4 ml-2" />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ImprovedOnboardingWizard; 