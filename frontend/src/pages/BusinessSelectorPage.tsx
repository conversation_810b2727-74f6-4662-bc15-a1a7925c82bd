import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Badge } from '../components/ui/badge';
import { Building2, Plus, Crown, Users, ArrowRight, CheckCircle, AlertCircle, Clock, Sparkles, Zap, TrendingUp, Globe, Mail, Phone, Bot, MessageCircle, BookOpen } from 'lucide-react';
import type { Business } from '../types/api';

const BusinessSelectorPage: React.FC = () => {
  const { user, businesses, switchBusiness } = useAuth();
  const navigate = useNavigate();

  const handleSelectBusiness = (business: Business) => {
    switchBusiness(business.id);
    navigate('/dashboard');
  };

  const handleCreateBusiness = () => {
    navigate('/create-business');
  };

  const getBusinessStatusBadge = (business: Business) => {
    if (business.plan === 'ENTERPRISE') {
      return <Badge variant="default" className="bg-purple-500 hover:bg-purple-600"><Crown className="w-3 h-3 mr-1" />Enterprise</Badge>;
    }
    if (business.plan === 'PROFESSIONAL') {
      return <Badge variant="default" className="bg-blue-500 hover:bg-blue-600">Professional</Badge>;
    }
    return <Badge variant="secondary" className="bg-gray-100 text-gray-700">Starter</Badge>;
  };

  const getBusinessHealthStatus = (business: Business) => {
    // Mock health status - in real app, this would be based on actual metrics
    const isHealthy = Math.random() > 0.3; // 70% chance of being healthy
    const hasRecentActivity = Math.random() > 0.5; // 50% chance of recent activity
    
    if (isHealthy && hasRecentActivity) {
      return { status: 'healthy', icon: CheckCircle, color: 'text-green-500', text: 'All systems operational' };
    } else if (isHealthy) {
      return { status: 'idle', icon: Clock, color: 'text-yellow-500', text: 'No recent activity' };
    } else {
      return { status: 'warning', icon: AlertCircle, color: 'text-orange-500', text: 'Needs attention' };
    }
  };

  const getBusinessDescription = (business: Business) => {
    const description = [];
    if (business.industry) description.push(business.industry);
    if (business.website) description.push('Website integrated');
    return description.join(' • ') || 'No description available';
  };

  const getBusinessMetrics = (business: Business) => {
    // Mock metrics - in real app, these would come from the backend
    return {
      agents: Math.floor(Math.random() * 5) + 1,
      conversations: Math.floor(Math.random() * 100) + 10,
      satisfaction: (Math.random() * 20 + 80).toFixed(1), // 80-100%
      responseTime: Math.floor(Math.random() * 2000) + 500 // 500-2500ms
    };
  };

  return (
    <div className="min-h-screen bg-gradient-subtle py-12 px-4">
      <div className="max-w-6xl mx-auto">
        {/* Enhanced Header */}
        <div className="text-center mb-12">
          <div className="relative inline-block mb-6">
            <div className="w-20 h-20 bg-gradient-primary rounded-full flex items-center justify-center mx-auto">
              <Building2 className="w-10 h-10 text-white" />
            </div>
            <div className="absolute -top-2 -right-2 w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
              <Sparkles className="w-5 h-5 text-white" />
            </div>
          </div>
          <h1 className="text-4xl font-bold text-foreground mb-3">
            Welcome back, {user?.firstName}! 👋
          </h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Choose a business to manage or create a new one to get started
          </p>
        </div>

        {/* Business Cards Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {businesses.map((business) => {
            const healthStatus = getBusinessHealthStatus(business);
            const metrics = getBusinessMetrics(business);
            const HealthIcon = healthStatus.icon;

            return (
              <Card 
                key={business.id} 
                className="cursor-pointer hover:shadow-elegant transition-all duration-300 hover:-translate-y-1 border-0 shadow-soft bg-card group"
                onClick={() => handleSelectBusiness(business)}
              >
                <CardHeader className="pb-4">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div className="w-12 h-12 bg-gradient-primary rounded-lg flex items-center justify-center">
                        <Building2 className="w-6 h-6 text-white" />
                      </div>
                      <div>
                        <CardTitle className="text-lg group-hover:text-blue-600 transition-colors">{business.name}</CardTitle>
                        <CardDescription className="text-sm">
                          {getBusinessDescription(business)}
                        </CardDescription>
                      </div>
                    </div>
                    <div className="flex flex-col items-end gap-2">
                      {getBusinessStatusBadge(business)}
                      <div className={`flex items-center gap-1 text-xs ${healthStatus.color}`}>
                        <HealthIcon className="w-3 h-3" />
                        <span>{healthStatus.text}</span>
                      </div>
                    </div>
                  </div>

                  {/* Business Metrics */}
                  <div className="grid grid-cols-2 gap-3 text-xs">
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <Bot className="w-3 h-3" />
                      <span>{metrics.agents} agents</span>
                    </div>
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <MessageCircle className="w-3 h-3" />
                      <span>{metrics.conversations} convos</span>
                    </div>
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <TrendingUp className="w-3 h-3" />
                      <span>{metrics.satisfaction}% satisfaction</span>
                    </div>
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <Zap className="w-3 h-3" />
                      <span>{metrics.responseTime}ms avg</span>
                    </div>
                  </div>
                </CardHeader>
                
                <CardContent className="pt-0">
                  <div className="space-y-4">
                    {/* Contact Info */}
                    <div className="space-y-2">
                      {business.email && (
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                          <Mail className="w-3 h-3" />
                          <span className="truncate">{business.email}</span>
                        </div>
                      )}
                      {business.website && (
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                          <Globe className="w-3 h-3" />
                          <span className="truncate">{business.website}</span>
                        </div>
                      )}
                    </div>
                    
                    {/* Action Button */}
                    <div className="pt-2 border-t border-border">
                      <Button 
                        className="w-full group/btn bg-gradient-primary hover:opacity-90"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleSelectBusiness(business);
                        }}
                      >
                        Manage Business
                        <ArrowRight className="w-4 h-4 ml-2 group-hover/btn:translate-x-1 transition-transform" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}

          {/* Enhanced Create New Business Card */}
          <Card 
            className="cursor-pointer hover:shadow-elegant transition-all duration-300 hover:-translate-y-1 border-2 border-dashed border-muted-foreground/30 hover:border-blue-500 bg-card/50 hover:bg-card group"
            onClick={handleCreateBusiness}
          >
            <CardHeader className="pb-4">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-12 h-12 bg-gradient-primary/20 rounded-lg flex items-center justify-center group-hover:bg-gradient-primary transition-colors">
                  <Plus className="w-6 h-6 text-muted-foreground group-hover:text-white transition-colors" />
                </div>
                <div>
                  <CardTitle className="text-lg text-muted-foreground group-hover:text-foreground transition-colors">
                    Create New Business
                  </CardTitle>
                  <CardDescription className="text-sm">
                    Set up another business account
                  </CardDescription>
                </div>
              </div>

              {/* Benefits */}
              <div className="space-y-2 text-xs text-muted-foreground">
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-3 h-3 text-green-500" />
                  <span>Free 14-day trial</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-3 h-3 text-green-500" />
                  <span>Setup in 5 minutes</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-3 h-3 text-green-500" />
                  <span>24/7 support included</span>
                </div>
              </div>
            </CardHeader>
            
            <CardContent className="pt-0">
              <div className="pt-2">
                <Button 
                  variant="outline" 
                  className="w-full group/btn border-dashed"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleCreateBusiness();
                  }}
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Add Business
                  <ArrowRight className="w-4 h-4 ml-2 group-hover/btn:translate-x-1 transition-transform" />
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Enhanced Stats Section */}
        {businesses.length > 0 && (
          <div className="bg-card border border-border rounded-lg p-6 mb-8">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-6 text-sm text-muted-foreground">
                <div className="flex items-center gap-2">
                  <Users className="w-4 h-4" />
                  <span className="font-medium">{businesses.length} business{businesses.length > 1 ? 'es' : ''}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Building2 className="w-4 h-4" />
                  <span>All systems operational</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span>Ready to scale</span>
                </div>
              </div>
              <div className="text-right">
                <div className="text-sm font-medium text-foreground">Total Value</div>
                <div className="text-lg font-bold text-blue-600">$2,450/mo</div>
              </div>
            </div>
          </div>
        )}

        {/* Enhanced Help Section */}
        <div className="text-center space-y-4">
          <div className="bg-card border border-border rounded-lg p-6 max-w-2xl mx-auto">
            <h3 className="text-lg font-semibold mb-2">Need Help Managing Your Businesses?</h3>
            <p className="text-muted-foreground mb-4">
              Our support team is here to help you get the most out of PalAI
            </p>
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <Button variant="outline" size="sm">
                <BookOpen className="w-4 h-4 mr-2" />
                View Documentation
              </Button>
              <Button variant="outline" size="sm">
                <MessageCircle className="w-4 h-4 mr-2" />
                Live Chat Support
              </Button>
              <Button variant="outline" size="sm">
                <Mail className="w-4 h-4 mr-2" />
                Email Support
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BusinessSelectorPage; 