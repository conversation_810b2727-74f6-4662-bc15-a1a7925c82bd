import React, { useState, useEffect } from 'react';
import { But<PERSON> } from './button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './card';
import { CheckCircle, X, Spark<PERSON>, PartyPop<PERSON>, Trophy, Star, Zap } from 'lucide-react';

interface SuccessFeedbackProps {
  title: string;
  message?: string;
  show: boolean;
  onClose: () => void;
  type?: 'success' | 'achievement' | 'milestone';
  autoClose?: boolean;
  autoCloseDelay?: number;
  showCelebration?: boolean;
}

export const SuccessFeedback: React.FC<SuccessFeedbackProps> = ({
  title,
  message,
  show,
  onClose,
  type = 'success',
  autoClose = true,
  autoCloseDelay = 3000,
  showCelebration = true,
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [showConfetti, setShowConfetti] = useState(false);

  useEffect(() => {
    if (show) {
      setIsVisible(true);
      if (showCelebration) {
        setShowConfetti(true);
        setTimeout(() => setShow<PERSON>on<PERSON>tti(false), 2000);
      }
      if (autoClose) {
        const timer = setTimeout(() => {
          handleClose();
        }, autoCloseDelay);
        return () => clearTimeout(timer);
      }
    } else {
      setIsVisible(false);
    }
  }, [show, autoClose, autoCloseDelay, showCelebration]);

  const handleClose = () => {
    setIsVisible(false);
    setTimeout(() => onClose(), 300); // Wait for animation to complete
  };

  const getIcon = () => {
    switch (type) {
      case 'achievement':
        return <Trophy className="w-6 h-6 text-yellow-500" />;
      case 'milestone':
        return <Star className="w-6 h-6 text-purple-500" />;
      default:
        return <CheckCircle className="w-6 h-6 text-green-500" />;
    }
  };

  const getBackgroundColor = () => {
    switch (type) {
      case 'achievement':
        return 'bg-yellow-50 border-yellow-200';
      case 'milestone':
        return 'bg-purple-50 border-purple-200';
      default:
        return 'bg-green-50 border-green-200';
    }
  };

  if (!show) return null;

  return (
    <>
      {/* Confetti Animation */}
      {showConfetti && (
        <div className="fixed inset-0 pointer-events-none z-50">
          {[...Array(20)].map((_, i) => (
            <div
              key={i}
              className="absolute animate-bounce"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 2}s`,
                animationDuration: `${1 + Math.random() * 2}s`,
              }}
            >
              <Sparkles className="w-4 h-4 text-yellow-400" />
            </div>
          ))}
        </div>
      )}

      {/* Success Card */}
      <div className="fixed top-4 right-4 z-50 max-w-sm w-full">
        <Card 
          className={`border-0 shadow-elegant transition-all duration-300 ${
            isVisible 
              ? 'translate-x-0 opacity-100 scale-100' 
              : 'translate-x-full opacity-0 scale-95'
          } ${getBackgroundColor()}`}
        >
          <CardHeader className="pb-3">
            <div className="flex items-start justify-between">
              <div className="flex items-center gap-3">
                <div className="relative">
                  {getIcon()}
                  {showCelebration && (
                    <div className="absolute -top-1 -right-1">
                      <div className="w-3 h-3 bg-yellow-400 rounded-full animate-pulse" />
                    </div>
                  )}
                </div>
                <div>
                  <CardTitle className="text-lg">{title}</CardTitle>
                  {message && (
                    <CardDescription className="text-sm mt-1">
                      {message}
                    </CardDescription>
                  )}
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleClose}
                className="h-8 w-8 p-0 hover:bg-white/20"
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
          </CardHeader>
          
          {showCelebration && (
            <CardContent className="pt-0">
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <PartyPopper className="w-4 h-4" />
                <span>Great job! Keep it up!</span>
              </div>
            </CardContent>
          )}
        </Card>
      </div>
    </>
  );
};

// Hook for easy success feedback management
export const useSuccessFeedback = () => {
  const [successState, setSuccessState] = useState<{
    show: boolean;
    title: string;
    message: string;
    type: 'success' | 'achievement' | 'milestone';
  }>({
    show: false,
    title: '',
    message: '',
    type: 'success',
  });

  const showSuccess = (
    title: string, 
    message?: string, 
    type: 'success' | 'achievement' | 'milestone' = 'success'
  ) => {
    setSuccessState({ show: true, title, message, type });
  };

  const hideSuccess = () => {
    setSuccessState(prev => ({ ...prev, show: false }));
  };

  return {
    successState,
    showSuccess,
    hideSuccess,
  };
};

// Success feedback with different types
export const SuccessFeedbackProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { successState, hideSuccess } = useSuccessFeedback();

  return (
    <>
      {children}
      <SuccessFeedback
        show={successState.show}
        title={successState.title}
        message={successState.message}
        type={successState.type}
        onClose={hideSuccess}
      />
    </>
  );
};

// Predefined success messages
export const SUCCESS_MESSAGES = {
  AGENT_CREATED: {
    title: 'Agent Created Successfully!',
    message: 'Your AI assistant is ready to help your customers.',
    type: 'success' as const,
  },
  BUSINESS_CREATED: {
    title: 'Business Setup Complete!',
    message: 'Your business is now configured and ready to go.',
    type: 'milestone' as const,
  },
  SETTINGS_SAVED: {
    title: 'Settings Saved!',
    message: 'Your preferences have been updated successfully.',
    type: 'success' as const,
  },
  FIRST_AGENT: {
    title: 'First Agent Created! 🎉',
    message: 'Congratulations on creating your first AI assistant!',
    type: 'achievement' as const,
  },
  PLAN_UPGRADED: {
    title: 'Plan Upgraded!',
    message: 'You now have access to advanced features.',
    type: 'milestone' as const,
  },
} as const; 