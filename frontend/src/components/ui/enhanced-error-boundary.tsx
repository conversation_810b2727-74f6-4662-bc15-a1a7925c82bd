import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Button } from './button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './card';
import { AlertTriangle, RefreshCw, Home, ArrowLeft, Bug, MessageCircle } from 'lucide-react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
  showDetails: boolean;
}

export class EnhancedErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false,
    showDetails: false,
  };

  public static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error, showDetails: false };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Error caught by boundary:', error, errorInfo);
    this.setState({ error, errorInfo });
    
    // In production, you would send this to your error reporting service
    // Example: Sentry.captureException(error, { extra: errorInfo });
  }

  private handleRetry = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  private handleGoHome = () => {
    window.location.href = '/dashboard';
  };

  private handleGoBack = () => {
    window.history.back();
  };

  private handleReportError = () => {
    const { error, errorInfo } = this.state;
    const errorReport = {
      message: error?.message,
      stack: error?.stack,
      componentStack: errorInfo?.componentStack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
    };

    // In production, this would send to your error reporting service
    console.log('Error Report:', errorReport);
    
    // For now, just copy to clipboard
    navigator.clipboard.writeText(JSON.stringify(errorReport, null, 2));
    alert('Error details copied to clipboard. Please send this to support.');
  };

  private toggleDetails = () => {
    this.setState(prev => ({ showDetails: !prev.showDetails }));
  };

  public render() {
    if (this.state.hasError) {
      const { error, showDetails } = this.state;

      return (
        <div className="min-h-screen bg-gradient-subtle flex items-center justify-center p-4">
          <div className="max-w-2xl w-full">
            <Card className="border-0 shadow-soft bg-card">
              <CardHeader className="text-center">
                <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <AlertTriangle className="w-8 h-8 text-red-600" />
                </div>
                <CardTitle className="text-2xl text-red-600">Something went wrong</CardTitle>
                <CardDescription className="text-lg">
                  We encountered an unexpected error. Don't worry, your data is safe.
                </CardDescription>
              </CardHeader>
              
              <CardContent className="space-y-6">
                {/* Error Message */}
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <p className="text-red-800 font-medium mb-2">Error Details:</p>
                  <p className="text-red-700 text-sm">{error?.message || 'An unknown error occurred'}</p>
                </div>

                {/* Recovery Actions */}
                <div className="space-y-3">
                  <Button 
                    onClick={this.handleRetry} 
                    className="w-full bg-gradient-primary hover:opacity-90"
                  >
                    <RefreshCw className="w-4 h-4 mr-2" />
                    Try Again
                  </Button>
                  
                  <div className="grid grid-cols-2 gap-3">
                    <Button 
                      variant="outline" 
                      onClick={this.handleGoBack}
                      className="w-full"
                    >
                      <ArrowLeft className="w-4 h-4 mr-2" />
                      Go Back
                    </Button>
                    <Button 
                      variant="outline" 
                      onClick={this.handleGoHome}
                      className="w-full"
                    >
                      <Home className="w-4 h-4 mr-2" />
                      Go Home
                    </Button>
                  </div>
                </div>

                {/* Additional Help */}
                <div className="border-t border-border pt-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Bug className="w-4 h-4" />
                      <span>Need help?</span>
                    </div>
                    <div className="flex gap-2">
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={this.toggleDetails}
                      >
                        {showDetails ? 'Hide' : 'Show'} Details
                      </Button>
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={this.handleReportError}
                      >
                        <MessageCircle className="w-4 h-4 mr-1" />
                        Report
                      </Button>
                    </div>
                  </div>
                </div>

                {/* Error Details (Collapsible) */}
                {showDetails && error && (
                  <div className="border border-border rounded-lg p-4 bg-muted">
                    <h4 className="font-medium mb-2">Technical Details</h4>
                    <div className="space-y-2 text-xs">
                      <div>
                        <strong>Error:</strong> {error.name}
                      </div>
                      <div>
                        <strong>Message:</strong> {error.message}
                      </div>
                      <div>
                        <strong>Stack:</strong>
                        <pre className="mt-1 p-2 bg-background rounded text-xs overflow-auto max-h-32">
                          {error.stack}
                        </pre>
                      </div>
                    </div>
                  </div>
                )}

                {/* Support Contact */}
                <div className="text-center text-sm text-muted-foreground">
                  <p>
                    If this problem persists, please contact our support team at{' '}
                    <a 
                      href="mailto:<EMAIL>" 
                      className="text-blue-600 hover:text-blue-700 underline"
                    >
                      <EMAIL>
                    </a>
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// Hook for functional components to trigger error boundary
export const useErrorHandler = () => {
  const throwError = (error: Error) => {
    throw error;
  };

  return { throwError };
};

// Higher-order component for error handling
export const withErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>,
  fallback?: ReactNode
) => {
  const WrappedComponent = (props: P) => (
    <EnhancedErrorBoundary fallback={fallback}>
      <Component {...props} />
    </EnhancedErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;
  return WrappedComponent;
}; 