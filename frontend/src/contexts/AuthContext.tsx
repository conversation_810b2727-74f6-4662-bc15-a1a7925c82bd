import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { useNavigate } from 'react-router-dom';
import { authService } from '../services/authService';
import { businessService } from '../services/businessService';
import type { Business } from '../types/api';

const AuthContext = createContext<AuthContextType | undefined>(undefined);
const AuthContext = createContext<AuthContextType | undefined>(undefined);
  const [token, setToken] = useState<string | null>(authService.getStoredToken());
  const [currentBusiness, setCurrentBusiness] = useState<Business | null>(null);
  const [businesses, setBusinesses] = useState<Business[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingBusinesses, setIsLoadingBusinesses] = useState(false);
  const navigate = useNavigate();

  // Initialize auth state on mount
  useEffect(() => {
    initializeAuth();
  }, []);

  useEffect(() => {
    if (user) {
      loadUserBusinesses();
    } else {
      setBusinesses([]);
      setCurrentBusiness(null);
    }
  }, [user]);

  const initializeAuth = async () => {
    try {
      const storedToken = authService.getStoredToken();
      if (storedToken && authService.isAuthenticated()) {
        setToken(storedToken);
        await loadCurrentUser();
      } else {
        // Clear invalid token
        authService.removeStoredToken();
        setToken(null);
        setUser(null);
      }
    } catch (error) {
      console.error('Failed to load current user:', error);
      // Invalid token, clear auth state
      authService.removeStoredToken();
      setToken(null);
      setUser(null);
    }
  };

  const loadUserBusinesses = async () => {
    // Prevent concurrent calls that could cause race conditions
    if (isLoadingBusinesses) {
      console.log('AuthContext: Skipping business loading - already in progress');
      return;
    }
    
    try {
      setIsLoadingBusinesses(true);
      console.log('AuthContext: Starting to load user businesses...');
      const userBusinesses = await businessService.getUserBusinesses();
      console.log('AuthContext: Received businesses from API:', userBusinesses);
      console.log('AuthContext: Number of businesses:', userBusinesses?.length || 0);
      
      if (userBusinesses && userBusinesses.length > 0) {
        setBusinesses(userBusinesses);
        
        // Set current business if none selected
        if (!currentBusiness) {
          const storedBusinessId = localStorage.getItem('current_business_id');
          const businessToSelect = storedBusinessId 
            ? userBusinesses.find(b => b.id.toString() === storedBusinessId) 
            : userBusinesses[0];
          
          if (businessToSelect) {
            console.log('AuthContext: Auto-selecting business:', businessToSelect.name);
            setCurrentBusiness(businessToSelect);
            localStorage.setItem('current_business_id', businessToSelect.id.toString());
          }
        }
      } else {
        console.log('AuthContext: No businesses found, setting empty array');
        setBusinesses([]);
      }
    } catch (error) {
      console.error('Failed to load user businesses:', error);
      setBusinesses([]);
    } finally {
      setIsLoadingBusinesses(false);
    }
  };

  const login = async (credentials: LoginRequest) => {
    try {
      setIsLoading(true);
      const response = await authService.login(credentials);
      
      setToken(response.token);
      setUser(response.user);
      authService.setAuthToken(response.token);

      if (response.needsEmailVerification) {
        navigate('/verify-email');
      } else {
        // Redirect directly to dashboard - will show appropriate state based on businesses
        navigate('/dashboard');
      }
    } catch (error) {
      console.error('Login failed:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const signup = async (userData: SignupRequest) => {
    try {
      setIsLoading(true);
      const response = await authService.signup(userData);
      
      setToken(response.token);
      setUser(response.user);
      authService.setAuthToken(response.token);

      if (response.needsEmailVerification) {
        navigate('/verify-email');
      } else {
        // Redirect directly to dashboard - will show empty state if no businesses
        navigate('/dashboard');
      }
    } catch (error) {
      console.error('Signup failed:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    setUser(null);
    setToken(null);
    setCurrentBusiness(null);
    setBusinesses([]);
    authService.removeStoredToken();
    localStorage.removeItem('current_business_id');
    navigate('/');
  };

  const refreshUser = async () => {
    if (!token) return;
    
    try {
      await loadCurrentUser();
      await loadUserBusinesses();
    } catch (error) {
      console.error('Failed to refresh user:', error);
      // If refresh fails, logout user
      logout();
    }
  };

  const switchBusiness = (businessId: number) => {
    const business = businesses.find(b => b.id === businessId);
    if (business) {
      setCurrentBusiness(business);
      localStorage.setItem('current_business_id', businessId.toString());
    }
  };

  const verifyEmail = async (token: string) => {
    try {
      await authService.verifyEmail({ token });
      // Refresh user data to update email verification status
      await loadCurrentUser();
    } catch (error) {
      console.error('Email verification failed:', error);
      throw error;
    }
  };

  const resetPassword = async (email: string) => {
    try {
      await authService.initiatePasswordReset({ email });
    } catch (error) {
      console.error('Password reset failed:', error);
      throw error;
    }
  };

  const confirmPasswordReset = async (token: string, newPassword: string) => {
    try {
      await authService.resetPassword({ token, newPassword });
    } catch (error) {
      console.error('Password reset confirmation failed:', error);
      throw error;
    }
  };

  const contextValue: AuthContextType = {
    user,
    token,
    currentBusiness,
    businesses,
    isLoading,
    login,
    signup,
    logout,
    refreshUser,
    switchBusiness,
    verifyEmail,
    resetPassword,
    confirmPasswordReset,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}; 