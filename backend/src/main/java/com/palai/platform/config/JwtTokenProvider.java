package com.palai.platform.config;

import com.palai.platform.model.entity.User;
import io.jsonwebtoken.*;
import io.jsonwebtoken.security.Keys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.crypto.SecretKey;
import java.util.Base64;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * JWT Token Provider for authentication and authorization
 */
@Component
@Slf4j
public class JwtTokenProvider {

    private final SecretKey key;
    private final long tokenValidityInMillis;

    public JwtTokenProvider(
            @Value("${palai.security.jwt-secret:your-256-bit-secret-key-here-change-in-production}") String secret,
            @Value("${palai.security.jwt-expiration:86400}") long tokenValidityInSeconds) {
        
        // JWT Best Practice: Use a strong, randomly generated key
        // If no secret is provided or it's the default, generate a secure key
        // Use provided secret but ensure it's properly padded for HS256 (256 bits / 32 bytes)
        byte[] secretBytes = secret.getBytes();
        if (secretBytes.length < 32) {
            // Pad the secret to meet minimum length requirement
            byte[] paddedSecret = new byte[32];
            System.arraycopy(secretBytes, 0, paddedSecret, 0, secretBytes.length);
            this.key = Keys.hmacShaKeyFor(paddedSecret);
        } else {
            this.key = Keys.hmacShaKeyFor(secretBytes);
        }

        this.tokenValidityInMillis = tokenValidityInSeconds * 1000;
        log.info("JWT Token Provider initialized with {}s token validity", tokenValidityInSeconds);
    }

    /**
     * Generate JWT token for user
     * Best Practice: Include minimal necessary claims to reduce token size
     */
    public String generateToken(User user) {
        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + tokenValidityInMillis);

        // JWT Best Practice: Include only essential claims
        Map<String, Object> claims = new HashMap<>();
        claims.put("email", user.getEmail());
        claims.put("emailVerified", user.getEmailVerified());
        // Include user ID in subject as per JWT standard
        
        return Jwts.builder()
                .subject(user.getEmail()) // Use email as subject for easier lookup
                .claims(claims)
                .issuer("palai-platform") // Best Practice: Always set issuer
                .audience().add("palai-frontend").and() // Best Practice: Set audience
                .issuedAt(now)
                .expiration(expiryDate)
                .signWith(key) // Modern JJWT automatically selects HS256
                .compact();
    }

    /**
     * Generate refresh token (longer validity)
     * Best Practice: Refresh tokens should have longer expiry and different type
     */
    public String generateRefreshToken(User user) {
        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + (tokenValidityInMillis * 7)); // 7 times longer

        return Jwts.builder()
                .subject(user.getEmail())
                .claim("type", "refresh")
                .claim("email", user.getEmail())
                .issuer("palai-platform")
                .audience().add("palai-frontend").and()
                .issuedAt(now)
                .expiration(expiryDate)
                .signWith(key)
                .compact();
    }

    /**
     * Get user email from JWT token subject
     * Best Practice: Subject should contain the primary identifier (email in our case)
     */
    public String getUserEmailFromToken(String token) {
        Claims claims = Jwts.parser()
                .verifyWith(key)
                .build()
                .parseSignedClaims(token)
                .getPayload();
        
        return claims.getSubject(); // Subject now contains email
    }

    /**
     * Get email from JWT token
     */
    public String getEmailFromToken(String token) {
        Claims claims = Jwts.parser()
                .verifyWith(key)
                .build()
                .parseSignedClaims(token)
                .getPayload();
        
        return claims.get("email", String.class);
    }

    /**
     * Validate JWT token with comprehensive checks
     * Best Practice: Validate issuer, audience, and expiration
     */
    public boolean validateToken(String token) {
        try {
            Claims claims = Jwts.parser()
                .verifyWith(key)
                .requireIssuer("palai-platform") // Best Practice: Validate issuer
                .requireAudience("palai-frontend") // Best Practice: Validate audience
                .build()
                .parseSignedClaims(token)
                .getPayload();
                
            // Additional validation: ensure token is not a refresh token being used as access token
            String tokenType = claims.get("type", String.class);
            if ("refresh".equals(tokenType)) {
                log.warn("Refresh token used as access token");
                return false;
            }
            
            return true;
        } catch (ExpiredJwtException e) {
            log.debug("JWT token expired: {}", e.getMessage());
            return false;
        } catch (UnsupportedJwtException e) {
            log.error("Unsupported JWT token: {}", e.getMessage());
            return false;
        } catch (MalformedJwtException e) {
            log.error("Malformed JWT token: {}", e.getMessage());
            return false;
        } catch (SecurityException e) {
            log.error("JWT signature validation failed: {}", e.getMessage());
            return false;
        } catch (IllegalArgumentException e) {
            log.error("JWT token compact of handler are invalid: {}", e.getMessage());
            return false;
        } catch (JwtException e) {
            log.error("Invalid JWT token: {}", e.getMessage());
            return false;
        }
    }

    /**
     * Check if token is expired
     */
    public boolean isTokenExpired(String token) {
        try {
            Date expiration = Jwts.parser()
                .verifyWith(key)
                .build()
                .parseSignedClaims(token)
                .getPayload()
                .getExpiration();
            
            return expiration.before(new Date());
        } catch (JwtException | IllegalArgumentException e) {
            return true;
        }
    }

    /**
     * Get remaining validity time in milliseconds
     */
    public long getRemainingValidityTime(String token) {
        try {
            Date expiration = Jwts.parser()
                .verifyWith(key)
                .build()
                .parseSignedClaims(token)
                .getPayload()
                .getExpiration();
            
            return expiration.getTime() - System.currentTimeMillis();
        } catch (JwtException | IllegalArgumentException e) {
            return 0;
        }
    }

    /**
     * Extract all claims from token
     */
    public Claims getAllClaimsFromToken(String token) {
        return Jwts.parser()
            .verifyWith(key)
            .build()
            .parseSignedClaims(token)
            .getPayload();
    }

    /**
     * Check if token is a refresh token
     */
    public boolean isRefreshToken(String token) {
        try {
            Claims claims = getAllClaimsFromToken(token);
            return "refresh".equals(claims.get("type", String.class));
        } catch (JwtException | IllegalArgumentException e) {
            return false;
        }
    }
} 